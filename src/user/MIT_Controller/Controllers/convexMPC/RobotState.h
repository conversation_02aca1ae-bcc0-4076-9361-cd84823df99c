#ifndef _RobotState
#define _RobotState

#include <eigen3/Eigen/Dense>
#include "common_types.h"

using Eigen::Matrix;
using Eigen::Quaternionf;

#include "common_types.h"
class RobotState
{
    public:
        void set(flt* p, flt* v, flt* q, flt* w, flt* r, flt yaw);
        void setEffectiveMassAndInertia(flt effective_mass, const Matrix<fpt,3,3>& effective_I_body);
        //void compute_rotations();
        void print();
        Matrix<fpt,3,1> p,v,w;
        Matrix<fpt,3,4> r_feet;
        Matrix<fpt,3,3> R;
        Matrix<fpt,3,3> R_yaw;
        Matrix<fpt,3,3> I_body;
        Quaternionf q;
        fpt yaw;
        fpt m = 14;
        //fpt m = 50.236; //DH

        // 扰动补偿相关
        fpt m_effective = 14;  // 有效质量（机身+负载）
        Matrix<fpt,3,3> I_body_effective;  // 有效机体惯量
    //private:
};
#endif
