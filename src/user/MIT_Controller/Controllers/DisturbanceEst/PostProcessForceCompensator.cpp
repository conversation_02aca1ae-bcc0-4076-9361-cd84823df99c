#include "PostProcessForceCompensator.h"
#include <iostream>
#include <cmath>

PostProcessForceCompensator::PostProcessForceCompensator() {
    // 默认参数已在头文件中设置
}

bool PostProcessForceCompensator::compensateForces(
    const Vec3<float> original_forces[4],
    const Vec4<float>& contact_states,
    const Vec3<float> foot_positions[4],
    float payload_mass,
    const Vec3<float>& disturbance_torque,
    Vec3<float> compensated_forces[4]
) {
    // 1. 复制原始力作为起始点
    for (int i = 0; i < 4; i++) {
        compensated_forces[i] = original_forces[i];
    }
    
    // 2. 计算重力补偿
    Vec3<float> gravity_compensation[4];
    computeGravityCompensation(payload_mass, contact_states, gravity_compensation);
    
    // 3. 计算扰动力矩补偿
    Vec3<float> torque_compensation[4];
    computeTorqueCompensation(disturbance_torque, foot_positions, contact_states, torque_compensation);
    
    // 4. 应用补偿
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {  // 仅对接触腿进行补偿
            compensated_forces[i] += _gravity_compensation_weight * gravity_compensation[i];
            compensated_forces[i] += _torque_compensation_weight * torque_compensation[i];
        }
    }
    
    // 5. 应用约束
    bool constraints_satisfied = true;
    constraints_satisfied &= applyForceLimit(compensated_forces, contact_states);
    constraints_satisfied &= applyFrictionConstraints(compensated_forces, contact_states);
    
    // 6. 如果约束违反，尝试重分配
    if (!constraints_satisfied) {
        constraints_satisfied = redistributeForces(
            compensated_forces, foot_positions, contact_states, 
            payload_mass, disturbance_torque
        );
    }
    
    // 7. 调试输出
    if (_debug_enabled) {
        printDebugInfo(original_forces, compensated_forces, contact_states);
    }
    
    return constraints_satisfied;
}

void PostProcessForceCompensator::computeGravityCompensation(
    float payload_mass,
    const Vec4<float>& contact_states,
    Vec3<float> gravity_compensation[4]
) {
    // 计算接触腿数量
    int contact_count = getContactCount(contact_states);
    
    if (contact_count == 0) {
        for (int i = 0; i < 4; i++) {
            gravity_compensation[i].setZero();
        }
        return;
    }
    
    // 负载重力均匀分配到接触腿
    Vec3<float> payload_gravity = payload_mass * _gravity;
    Vec3<float> gravity_per_leg = payload_gravity / static_cast<float>(contact_count);
    
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            gravity_compensation[i] = gravity_per_leg;
        } else {
            gravity_compensation[i].setZero();
        }
    }
}

void PostProcessForceCompensator::computeTorqueCompensation(
    const Vec3<float>& disturbance_torque,
    const Vec3<float> foot_positions[4],
    const Vec4<float>& contact_states,
    Vec3<float> torque_compensation[4]
) {
    // 初始化
    for (int i = 0; i < 4; i++) {
        torque_compensation[i].setZero();
    }
    
    int contact_count = getContactCount(contact_states);
    if (contact_count == 0) return;
    
    // 构建接触腿的力矩平衡方程
    // τ_disturbance = Σ (r_i × F_compensation_i)
    // 使用最小二乘法求解力补偿
    
    Eigen::MatrixXf A(3, contact_count * 3);
    Eigen::VectorXf b(3);
    
    b << disturbance_torque[0], disturbance_torque[1], disturbance_torque[2];
    
    int col_idx = 0;
    std::vector<int> contact_legs;
    
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            contact_legs.push_back(i);
            
            // 构建反对称矩阵 [r×]
            Vec3<float> r = foot_positions[i];
            Eigen::Matrix3f skew;
            skew <<     0, -r[2],  r[1],
                     r[2],     0, -r[0],
                    -r[1],  r[0],     0;
            
            A.block<3, 3>(0, col_idx * 3) = skew;
            col_idx++;
        }
    }
    
    // 最小二乘求解
    Eigen::VectorXf force_compensation = A.completeOrthogonalDecomposition().solve(b);
    
    // 分配结果
    for (int i = 0; i < contact_legs.size(); i++) {
        int leg_idx = contact_legs[i];
        torque_compensation[leg_idx] = Vec3<float>(
            force_compensation[i * 3],
            force_compensation[i * 3 + 1],
            force_compensation[i * 3 + 2]
        );
    }
}

bool PostProcessForceCompensator::applyFrictionConstraints(
    Vec3<float> forces[4],
    const Vec4<float>& contact_states
) {
    bool all_satisfied = true;
    
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            float fx = forces[i][0];
            float fy = forces[i][1];
            float fz = forces[i][2];
            
            float tangential_force = std::sqrt(fx * fx + fy * fy);
            float max_tangential = _mu * std::abs(fz);
            
            if (tangential_force > max_tangential && max_tangential > 1e-6f) {
                // 缩放切向力以满足摩擦锥约束
                float scale = max_tangential / tangential_force;
                forces[i][0] *= scale;
                forces[i][1] *= scale;
                all_satisfied = false;
            }
        }
    }
    
    return all_satisfied;
}

bool PostProcessForceCompensator::applyForceLimit(
    Vec3<float> forces[4],
    const Vec4<float>& contact_states
) {
    bool all_satisfied = true;
    
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            float& fz = forces[i][2];
            
            if (fz < _f_min) {
                fz = _f_min;
                all_satisfied = false;
            } else if (fz > _f_max) {
                fz = _f_max;
                all_satisfied = false;
            }
        }
    }
    
    return all_satisfied;
}

bool PostProcessForceCompensator::redistributeForces(
    Vec3<float> forces[4],
    const Vec3<float> foot_positions[4],
    const Vec4<float>& contact_states,
    float payload_mass,
    const Vec3<float>& disturbance_torque
) {
    // 简化的重分配策略：
    // 1. 确保力平衡
    // 2. 在满足约束的前提下重新分配
    
    int contact_count = getContactCount(contact_states);
    if (contact_count == 0) return false;
    
    // 计算总的所需力和力矩
    Vec3<float> total_required_force = payload_mass * _gravity;
    Vec3<float> total_required_torque = disturbance_torque;
    
    // 简单策略：均匀分配法向力，然后调整以满足力矩平衡
    float fz_per_leg = -total_required_force[2] / contact_count;
    
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            forces[i][2] = std::max(_f_min, std::min(_f_max, fz_per_leg));
        } else {
            forces[i].setZero();
        }
    }
    
    // 应用约束
    applyForceLimit(forces, contact_states);
    applyFrictionConstraints(forces, contact_states);
    
    return true;
}

int PostProcessForceCompensator::getContactCount(const Vec4<float>& contact_states) {
    int count = 0;
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) count++;
    }
    return count;
}

void PostProcessForceCompensator::printDebugInfo(
    const Vec3<float> original_forces[4],
    const Vec3<float> compensated_forces[4],
    const Vec4<float>& contact_states
) {
    std::cout << "=== Force Compensation Debug ===" << std::endl;
    for (int i = 0; i < 4; i++) {
        if (contact_states[i] > 0.1f) {
            std::cout << "Leg " << i << ":" << std::endl;
            std::cout << "  Original:    [" << original_forces[i].transpose() << "]" << std::endl;
            std::cout << "  Compensated: [" << compensated_forces[i].transpose() << "]" << std::endl;
            std::cout << "  Delta:       [" << (compensated_forces[i] - original_forces[i]).transpose() << "]" << std::endl;
        }
    }
    std::cout << "================================" << std::endl;
}
