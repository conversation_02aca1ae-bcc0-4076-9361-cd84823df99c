# 扰动估计信息集成到Convex MPC控制的完整方案

## 概述

本方案实现了将DisturbanceEstimator估计的负载质量(mp_fused)和扰动力矩(I_world * d_est)集成到Locomotion状态下的Convex MPC控制中，使MPC能够：
1. 在质心动力学中反映有效质量/惯量的变化
2. 在合力/合力矩平衡中补偿额外重力和扰动力矩

## A) 数据通路设计与数据结构改动

### 1. 数据存储位置
- **DisturbanceEstimator**: 增加了MPC扰动补偿接口
  - `getEffectiveMass()`: 返回机身质量+负载质量
  - `getEffectiveInertiaWorld()`: 返回含负载影响的有效惯量矩阵
  - `getDisturbanceTorqueCompensation()`: 返回扰动力矩补偿

### 2. 数据读取接口
- **ConvexMPCLocomotion**: 添加了扰动估计器引用
  - `setDisturbanceEstimator()`: 设置扰动估计器引用
  - `setForceCompensator()`: 设置后处理力补偿器

### 3. 数据结构改动
- **RobotState**: 增加有效质量和惯量成员
  - `m_effective`: 有效质量
  - `I_body_effective`: 有效机体惯量
  - `setEffectiveMassAndInertia()`: 设置接口

## B) Dense MPC扰动补偿集成

### 1. 质心动力学修正
```cpp
// 在ct_ss_mats函数中使用有效质量
B.block(9,b*3,3,3) = Matrix<fpt,3,3>::Identity() / m_effective;
```

### 2. 角动力学修正
```cpp
// 使用有效惯量矩阵
I_world = rs.R_yaw * rs.I_body_effective * rs.R_yaw.transpose();
B.block(6,b*3,3,3) = cross_mat(I_world.inverse(), r_feet.col(b));
```

### 3. 接口扩展
- 新增`update_effective_mass_inertia()`接口函数
- 在`solveDenseMPC()`中调用扰动补偿更新

## C) Sparse MPC扰动补偿集成

### 1. SparseCMPC类扩展
```cpp
// 新增有效参数设置接口
template<typename T>
void setEffectiveRobotParameters(Mat3<T>& effective_inertia, T effective_mass);
```

### 2. 动力学建模修正
- 在`solveSparseMPC()`中更新有效质量和惯量
- SparseCMPC内部使用有效参数进行动力学建模

## D) Locomotion状态集成落地

### 1. FSM_State_Locomotion修改
```cpp
// 建立扰动估计器与MPC的连接
cMPCOld->setDisturbanceEstimator(_disturbance_estimator);
cMPCOld->setForceCompensator(_force_compensator);
```

### 2. 数据流连接
- 扰动估计器 → ConvexMPCLocomotion → MPC求解器
- 确保每个MPC周期都更新有效参数

## E) 关键公式与坐标系处理

### 1. 有效质量计算
```
m_effective = m_body + mp_fused
```

### 2. 有效惯量计算
```cpp
// 简化模型：负载主要影响yaw轴惯量
I_body_effective = I_body;
I_body_effective(2,2) += mp_fused * offset²;

// 转换到世界系
I_world_effective = R_yaw.transpose() * I_body_effective * R_yaw;
```

### 3. 扰动力矩补偿
```cpp
τ_disturbance = I_world * d_est;  // 世界系扰动力矩
```

### 4. 坐标系转换
- 世界系 ↔ 机体系: 使用完整旋转矩阵R_wb
- 世界系 ↔ Yaw系: 使用简化yaw旋转矩阵R_yaw

## F) 后处理力分配补偿方案

### 1. PostProcessForceCompensator类
工程化替代方案，不修改底层求解器，通过后处理进行力分配补偿。

### 2. 主要功能
- **重力补偿**: 将负载重力均匀分配到接触腿
- **力矩补偿**: 通过最小二乘法求解扰动力矩补偿
- **约束处理**: 确保摩擦锥和法向力限制
- **力重分配**: 约束违反时的重分配策略

### 3. 使用方式
```cpp
// 在solveDenseMPC()和solveSparseMPC()后调用
bool success = _forceCompensator->compensateForces(
    original_forces, contact_states, foot_positions,
    payload_mass, disturbance_torque, compensated_forces
);
```

## 实现要点

### 1. 最小侵入式设计
- Dense MPC: 仅修改质量和惯量参数传递
- Sparse MPC: 仅增加有效参数设置接口
- 后处理方案: 完全不修改底层求解器

### 2. 数据一致性
- 统一的坐标系转换
- 每个MPC周期更新有效参数
- 扰动估计与MPC同步

### 3. 工程化考虑
- 参数可配置（摩擦系数、力限制等）
- 调试输出支持
- 异常情况处理（约束违反、求解失败等）

## 使用建议

### 1. 参数调优
- 扰动观测器增益K
- 后处理补偿权重
- 摩擦系数和力限制

### 2. 性能监控
- MPC求解时间
- 约束违反频率
- 补偿效果评估

### 3. 渐进式部署
1. 首先使用后处理方案验证效果
2. 确认稳定后启用底层求解器修改
3. 根据实际效果调整参数

## 文件清单

### 新增文件
- `DisturbanceCompensation_Formulas.md`: 公式推导文档
- `PostProcessForceCompensator.h/cpp`: 后处理补偿器
- `Integration_Summary.md`: 本总结文档

### 修改文件
- `DisturbanceEstimator.h/cpp`: 增加MPC接口
- `ConvexMPCLocomotion.h/cpp`: 集成扰动补偿
- `SparseCMPC.h`: 增加有效参数接口
- `RobotState.h/cpp`: 增加有效参数支持
- `convexMPC_interface.h/cpp`: 增加参数传递接口
- `SolverMPC.cpp`: 使用有效参数
- `FSM_State_Locomotion.h/cpp`: 建立连接

## 测试建议

1. **单元测试**: 各个接口函数的正确性
2. **集成测试**: 数据流的完整性
3. **性能测试**: MPC求解时间和稳定性
4. **实机验证**: 负载条件下的控制效果
