#ifndef POST_PROCESS_FORCE_COMPENSATOR_H
#define POST_PROCESS_FORCE_COMPENSATOR_H

#include "cppTypes.h"
#include <eigen3/Eigen/Dense>

/**
 * 后处理力分配补偿器
 * 
 * 这是一个不修改底层MPC求解器的工程化替代方案，通过后处理的方式
 * 对MPC输出的足端力进行补偿，以考虑负载质量和扰动力矩的影响。
 * 
 * 主要功能：
 * 1. 补偿负载重力对足端力分配的影响
 * 2. 补偿扰动力矩对足端力分配的影响
 * 3. 确保补偿后的力满足摩擦锥约束
 * 4. 确保补偿后的法向力在合理范围内
 */
class PostProcessForceCompensator {
public:
    EIGEN_MAKE_ALIGNED_OPERATOR_NEW
    
    PostProcessForceCompensator();
    ~PostProcessForceCompensator() = default;
    
    /**
     * 对MPC输出的足端力进行扰动补偿
     * @param original_forces 原始MPC输出的足端力（世界系，4x3）
     * @param contact_states 接触状态（4x1，0-1之间）
     * @param foot_positions 足端位置相对质心（世界系，4x3）
     * @param payload_mass 负载质量
     * @param disturbance_torque 扰动力矩（世界系）
     * @param compensated_forces 输出：补偿后的足端力（世界系，4x3）
     * @return 是否成功补偿
     */
    bool compensateForces(
        const Vec3<float> original_forces[4],
        const Vec4<float>& contact_states,
        const Vec3<float> foot_positions[4],
        float payload_mass,
        const Vec3<float>& disturbance_torque,
        Vec3<float> compensated_forces[4]
    );
    
    /**
     * 设置摩擦系数
     */
    void setFrictionCoefficient(float mu) { _mu = mu; }
    
    /**
     * 设置法向力限制
     */
    void setForceLimit(float f_min, float f_max) { 
        _f_min = f_min; 
        _f_max = f_max; 
    }
    
    /**
     * 设置补偿权重
     */
    void setCompensationWeights(float gravity_weight, float torque_weight) {
        _gravity_compensation_weight = gravity_weight;
        _torque_compensation_weight = torque_weight;
    }
    
    /**
     * 启用/禁用调试输出
     */
    void enableDebug(bool enable) { _debug_enabled = enable; }

private:
    // 参数
    float _mu = 0.4f;                           // 摩擦系数
    float _f_min = 5.0f;                        // 最小法向力
    float _f_max = 200.0f;                      // 最大法向力
    float _gravity_compensation_weight = 1.0f;   // 重力补偿权重
    float _torque_compensation_weight = 0.8f;    // 力矩补偿权重
    bool _debug_enabled = false;                 // 调试输出
    
    // 重力常量
    Vec3<float> _gravity = Vec3<float>(0, 0, -9.81f);
    
    /**
     * 计算负载重力补偿
     */
    void computeGravityCompensation(
        float payload_mass,
        const Vec4<float>& contact_states,
        Vec3<float> gravity_compensation[4]
    );
    
    /**
     * 计算扰动力矩补偿
     */
    void computeTorqueCompensation(
        const Vec3<float>& disturbance_torque,
        const Vec3<float> foot_positions[4],
        const Vec4<float>& contact_states,
        Vec3<float> torque_compensation[4]
    );
    
    /**
     * 应用摩擦锥约束
     */
    bool applyFrictionConstraints(
        Vec3<float> forces[4],
        const Vec4<float>& contact_states
    );
    
    /**
     * 应用法向力限制
     */
    bool applyForceLimit(
        Vec3<float> forces[4],
        const Vec4<float>& contact_states
    );
    
    /**
     * 检查力平衡
     */
    bool checkForceBalance(
        const Vec3<float> forces[4],
        const Vec3<float> foot_positions[4],
        const Vec4<float>& contact_states,
        float payload_mass,
        const Vec3<float>& disturbance_torque
    );
    
    /**
     * 力重分配（当约束违反时）
     */
    bool redistributeForces(
        Vec3<float> forces[4],
        const Vec3<float> foot_positions[4],
        const Vec4<float>& contact_states,
        float payload_mass,
        const Vec3<float>& disturbance_torque
    );
    
    /**
     * 计算接触腿数量
     */
    int getContactCount(const Vec4<float>& contact_states);
    
    /**
     * 调试输出
     */
    void printDebugInfo(
        const Vec3<float> original_forces[4],
        const Vec3<float> compensated_forces[4],
        const Vec4<float>& contact_states
    );
};

#endif // POST_PROCESS_FORCE_COMPENSATOR_H
