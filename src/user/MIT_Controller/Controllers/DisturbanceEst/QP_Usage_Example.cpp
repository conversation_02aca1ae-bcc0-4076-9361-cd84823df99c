/*
 * QP求解器使用示例
 * 
 * 本文件展示了如何使用DisturbanceEstimator中的QP求解器
 * 来计算四足机器人的期望足端反作用力
 */

#include "DisturbanceEstimator.h"
#include <iostream>

// 示例：如何在控制循环中使用QP求解器
void example_usage() {
    std::cout << "=== QP求解器使用示例 ===" << std::endl;
    
    // 1. 创建DisturbanceEstimator实例
    float dt = 0.002f;  // 500Hz控制频率
    int iterations_between_mpc = 25;  // MPC频率为20Hz
    MIT_UserParameters* params = nullptr;  // 实际使用时需要提供参数
    
    DisturbanceEstimator estimator(dt, iterations_between_mpc, params);
    estimator.initialize();
    
    std::cout << "DisturbanceEstimator初始化完成" << std::endl;
    
    // 2. 设置QP求解器参数
    // 这些参数可以根据实际需要调整
    std::cout << "QP求解器参数:" << std::endl;
    std::cout << "  摩擦系数 μ = " << 0.4f << std::endl;
    std::cout << "  最大接触力 = " << 500.0f << " N" << std::endl;
    std::cout << "  接触阈值 = " << 0.5f << std::endl;
    
    // 3. 模拟控制循环
    std::cout << "\n=== 模拟控制循环 ===" << std::endl;
    
    // 注意：在实际使用中，这些数据来自ControlFSMData
    // 这里只是展示数据流
    
    /*
    // 伪代码示例：
    for(int i = 0; i < control_iterations; i++) {
        // 获取传感器数据和状态估计
        ControlFSMData<float> data = get_sensor_data();
        
        // 更新扰动估计器（包含QP求解）
        estimator.update(data);
        
        // 获取QP求解结果
        Vec3<float> foot_forces[4];
        for(int leg = 0; leg < 4; leg++) {
            foot_forces[leg] = estimator.foot_force_des[leg];
            
            // 使用期望足端力进行控制
            apply_foot_force_control(leg, foot_forces[leg]);
        }
    }
    */
    
    std::cout << "控制循环中的QP求解流程:" << std::endl;
    std::cout << "1. 获取传感器数据和状态估计" << std::endl;
    std::cout << "2. 调用 estimator.update(data)" << std::endl;
    std::cout << "3. QP求解器自动执行:" << std::endl;
    std::cout << "   - 分析接触状态" << std::endl;
    std::cout << "   - 构建缩减的QP问题" << std::endl;
    std::cout << "   - 求解优化问题" << std::endl;
    std::cout << "   - 更新 foot_force_des[4]" << std::endl;
    std::cout << "4. 使用期望足端力进行控制" << std::endl;
}

// 示例：QP求解器的输入输出
void input_output_example() {
    std::cout << "\n=== QP求解器输入输出示例 ===" << std::endl;
    
    std::cout << "输入数据:" << std::endl;
    std::cout << "  contact_state[4]: 接触状态 (0=摆动, 1=接触)" << std::endl;
    std::cout << "  pFoot[4]: 足端位置 (世界坐标系)" << std::endl;
    std::cout << "  position_control_force: PID位置控制力" << std::endl;
    std::cout << "  attitude_control_torque: PID姿态控制力矩" << std::endl;
    
    std::cout << "\n处理过程:" << std::endl;
    std::cout << "  1. 识别接触腿: contact_state[i] > 0.5" << std::endl;
    std::cout << "  2. 构建等式约束: A_eq * f = b_eq" << std::endl;
    std::cout << "     - 力平衡: Σf_i = F_desired" << std::endl;
    std::cout << "     - 力矩平衡: Σ(r_i × f_i) = T_desired" << std::endl;
    std::cout << "  3. 构建摩擦锥约束: L * f ≥ 0" << std::endl;
    std::cout << "  4. 求解: min ||f||² subject to constraints" << std::endl;
    
    std::cout << "\n输出结果:" << std::endl;
    std::cout << "  foot_force_des[4]: 期望足端反作用力" << std::endl;
    std::cout << "    - 接触腿: 优化求解的力值" << std::endl;
    std::cout << "    - 摆动腿: 自动设为零" << std::endl;
}

// 示例：不同接触状态下的行为
void contact_state_examples() {
    std::cout << "\n=== 不同接触状态示例 ===" << std::endl;
    
    std::cout << "情况1: 四腿支撑 (contact_state = [1,1,1,1])" << std::endl;
    std::cout << "  - 优化变量: 12个 (4腿 × 3分量)" << std::endl;
    std::cout << "  - 等式约束: 6个 (力平衡3 + 力矩平衡3)" << std::endl;
    std::cout << "  - 不等式约束: 20个 (4腿 × 5摩擦锥约束)" << std::endl;
    
    std::cout << "\n情况2: 三腿支撑 (contact_state = [1,1,1,0])" << std::endl;
    std::cout << "  - 优化变量: 9个 (3腿 × 3分量)" << std::endl;
    std::cout << "  - 等式约束: 6个" << std::endl;
    std::cout << "  - 不等式约束: 15个 (3腿 × 5摩擦锥约束)" << std::endl;
    std::cout << "  - 摆动腿力: foot_force_des[3] = [0,0,0]" << std::endl;
    
    std::cout << "\n情况3: 对角支撑 (contact_state = [1,0,1,0])" << std::endl;
    std::cout << "  - 优化变量: 6个 (2腿 × 3分量)" << std::endl;
    std::cout << "  - 等式约束: 6个" << std::endl;
    std::cout << "  - 不等式约束: 10个 (2腿 × 5摩擦锥约束)" << std::endl;
    std::cout << "  - 摆动腿力: foot_force_des[1] = foot_force_des[3] = [0,0,0]" << std::endl;
    
    std::cout << "\n情况4: 无接触 (contact_state = [0,0,0,0])" << std::endl;
    std::cout << "  - 跳过QP求解" << std::endl;
    std::cout << "  - 所有腿力设为零: foot_force_des[i] = [0,0,0]" << std::endl;
}

// 示例：参数调优建议
void parameter_tuning_guide() {
    std::cout << "\n=== 参数调优指南 ===" << std::endl;
    
    std::cout << "1. 摩擦系数 (mu):" << std::endl;
    std::cout << "   - 干燥混凝土: 0.6-0.8" << std::endl;
    std::cout << "   - 湿润地面: 0.3-0.5" << std::endl;
    std::cout << "   - 冰面/光滑表面: 0.1-0.2" << std::endl;
    std::cout << "   - 当前设置: 0.4 (适中值)" << std::endl;
    
    std::cout << "\n2. 最大接触力 (f_max):" << std::endl;
    std::cout << "   - 考虑机器人重量和动态载荷" << std::endl;
    std::cout << "   - 当前设置: 500N (适合中型四足机器人)" << std::endl;
    std::cout << "   - 调整建议: 根据实际测试调整" << std::endl;
    
    std::cout << "\n3. 接触阈值:" << std::parameter>
    std::cout << "   - 当前设置: 0.5" << std::endl;
    std::cout << "   - 过低: 可能误判摆动腿为接触" << std::endl;
    std::cout << "   - 过高: 可能误判接触腿为摆动" << std::endl;
    
    std::cout << "\n4. 正则化项:" << std::endl;
    std::cout << "   - 当前设置: 1e-6" << std::endl;
    std::cout << "   - 作用: 确保QP问题数值稳定性" << std::endl;
    std::cout << "   - 调整: 通常不需要修改" << std::endl;
}

int main() {
    example_usage();
    input_output_example();
    contact_state_examples();
    parameter_tuning_guide();
    
    std::cout << "\n=== 总结 ===" << std::endl;
    std::cout << "QP求解器已成功集成到DisturbanceEstimator中" << std::endl;
    std::cout << "主要特性:" << std::endl;
    std::cout << "✓ 使用qpOASES库求解QP问题" << std::endl;
    std::cout << "✓ 通过去除摆动腿减少计算规模" << std::endl;
    std::cout << "✓ 严格满足摩擦锥约束" << std::endl;
    std::cout << "✓ 自动处理不同接触状态" << std::endl;
    std::cout << "✓ 实时性能优化" << std::endl;
    
    return 0;
}
