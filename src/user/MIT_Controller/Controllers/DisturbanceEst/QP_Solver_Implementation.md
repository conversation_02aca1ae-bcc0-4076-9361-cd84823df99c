# QP求解器实现文档

## 概述

本文档描述了在DisturbanceEstimator类中实现的QP（二次规划）求解器，用于计算四足机器人的期望足端反作用力。该实现基于MIT控制器的方法，使用qpOASES库求解优化问题，并通过去除摆动腿的优化量来减少计算规模。

## 主要特性

### 1. 智能腿部状态管理
- **接触状态分析**: 自动识别接触腿和摆动腿
- **动态规模缩减**: 只对接触腿进行优化，显著减少计算量
- **阈值控制**: 使用0.5的阈值判断腿部接触状态

### 2. QP问题构建
- **等式约束**: 力平衡和力矩平衡约束 (6个约束)
- **不等式约束**: 摩擦锥约束 (每条接触腿5个约束)
- **目标函数**: 最小化足端力的二次型

### 3. 摩擦锥约束
```
Fx + μ*Fz ≥ 0    (正向摩擦约束)
-Fx + μ*Fz ≥ 0   (负向摩擦约束)
Fy + μ*Fz ≥ 0    (侧向摩擦约束)
-Fy + μ*Fz ≥ 0   (侧向摩擦约束)
Fz ≥ 0           (法向力非负约束)
```

## 实现细节

### 核心函数: `solveQP()`

#### 步骤1: 接触状态分析
```cpp
for(int i = 0; i < 4; i++){
    if(contact_state[i] > 0.5f){  // 接触阈值
        contact_leg_indices.push_back(i);
        num_contact_legs++;
    } else {
        swing_leg_indices.push_back(i);
    }
}
```

#### 步骤2: 构建缩减的QP问题
- **变量数量**: `num_contact_legs * 3` (每条接触腿3个力分量)
- **等式约束**: 6个 (3个力平衡 + 3个力矩平衡)
- **不等式约束**: `num_contact_legs * 5` (每条腿的摩擦锥约束)

#### 步骤3: 等式约束矩阵构建
```cpp
// 力平衡约束矩阵 A_eq (6 x reduced_vars)
A_eq.block<3, 3>(0, 3*i) = Identity(3, 3);           // 力平衡
A_eq.block<3, 3>(3, 3*i) = skew(pFoot[leg_idx]);     // 力矩平衡
```

#### 步骤4: 摩擦锥约束
```cpp
friction_block << μ, 0,  1.0f,   // Fx + μ*Fz ≥ 0
                 -μ, 0,  1.0f,   // -Fx + μ*Fz ≥ 0  
                  0,  μ, 1.0f,   // Fy + μ*Fz ≥ 0
                  0, -μ, 1.0f,   // -Fy + μ*Fz ≥ 0
                  0,  0,  1.0f;  // Fz ≥ 0
```

#### 步骤5: qpOASES求解
- 使用qpOASES::QProblem类
- 设置MPC优化选项
- 1000次最大迭代限制
- 10ms时间限制

### 关键参数

| 参数 | 值 | 说明 |
|------|----|----- |
| mu | 0.4 | 摩擦系数 |
| f_max | 500.0 N | 最大接触力限制 |
| 接触阈值 | 0.5 | 判断腿部接触的阈值 |
| 正则化项 | 1e-6 | 目标函数的正则化权重 |

## 输出结果

### 1. foot_force_des[4]
- **类型**: Vec3<float>[4]
- **内容**: 四条腿的期望足端反作用力
- **单位**: 牛顿 (N)
- **坐标系**: 世界坐标系

### 2. 摆动腿处理
- 摆动腿的期望力自动设为零
- 只有接触腿参与优化计算
- 显著减少计算复杂度

## 调试功能

### 1. 状态输出
```cpp
std::cout << "Contact legs: " << num_contact_legs << std::endl;
```

### 2. 求解结果输出 (每100次)
```cpp
for(int i = 0; i < 4; i++){
    std::cout << "Leg " << i << " force: [" 
              << foot_force_des[i][0] << ", " 
              << foot_force_des[i][1] << ", " 
              << foot_force_des[i][2] << "]" << std::endl;
}
```

### 3. 性能监控
```cpp
std::cout << "QP solved successfully in " << cpu_time * 1000 << " ms" << std::endl;
```

## 集成方式

QP求解器在`DisturbanceEstimator::update()`函数中被调用：

```cpp
void DisturbanceEstimator::update(const ControlFSMData<float>& data) {
    _SetupCommand(data);
    updateTauEst(data);
    updatePIDControllers(data);
    
    T_b = attitude_control_torque;  // 期望质心扭矩
    F_b = position_control_force;   // 期望质心合外力
    
    // 求解QP问题，计算期望的足端反作用力
    solveQP();
}
```

## 优势

1. **计算效率**: 通过去除摆动腿，显著减少优化变量数量
2. **物理约束**: 严格满足摩擦锥约束和力平衡约束
3. **鲁棒性**: 处理无接触腿的边界情况
4. **实时性**: 使用高效的qpOASES求解器
5. **可扩展性**: 易于调整参数和约束条件

## 未来改进方向

1. **自适应摩擦系数**: 根据地面条件动态调整μ值
2. **力分布优化**: 添加力分布均匀性的目标函数项
3. **预测控制**: 结合未来接触状态的预测信息
4. **约束软化**: 在约束冲突时使用软约束处理
