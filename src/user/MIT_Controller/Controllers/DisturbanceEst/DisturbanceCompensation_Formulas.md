# 扰动补偿关键公式推导与坐标系处理

## 1. 坐标系定义

### 1.1 坐标系说明
- **世界坐标系 (W)**: 固定在地面，Z轴向上
- **机体坐标系 (B)**: 固定在机器人质心，随机器人运动
- **Yaw坐标系**: 仅考虑yaw角旋转的简化世界坐标系

### 1.2 旋转矩阵定义
```cpp
// 世界系到机体系的旋转矩阵
Mat3<float> R_wb = seResult.rBody;  // world->body

// Yaw旋转矩阵（仅绕Z轴旋转）
Mat3<float> R_yaw;
R_yaw << cos(yaw), -sin(yaw), 0,
         sin(yaw),  cos(yaw), 0,
         0,         0,        1;
```

## 2. 质心动力学方程修正

### 2.1 原始质心动力学
```
m * a_com = Σ F_foot + m * g
```

### 2.2 含负载的质心动力学
```
(m_body + m_payload) * a_com = Σ F_foot + (m_body + m_payload) * g
```

其中：
- `m_body`: 机身质量 (14 kg)
- `m_payload`: 负载质量 (mp_fused)
- `m_effective = m_body + m_payload`

### 2.3 MPC中的实现
在Dense MPC的ct_ss_mats函数中：
```cpp
// B矩阵的线性动力学部分
B.block(9,b*3,3,3) = Matrix<fpt,3,3>::Identity() / m_effective;
```

## 3. 角动力学方程修正

### 3.1 原始角动力学
```
I_world * ω̇ + ω × (I_world * ω) = Σ (r_foot × F_foot)
```

### 3.2 含负载和扰动的角动力学
```
I_world_effective * ω̇ + ω × (I_world_effective * ω) = Σ (r_foot × F_foot) + τ_disturbance
```

其中：
- `I_world_effective`: 有效惯量矩阵（含负载影响）
- `τ_disturbance`: 扰动力矩补偿项

### 3.3 有效惯量计算
```cpp
// 机体系有效惯量（简化模型）
I_body_effective = I_body + m_payload * r_payload_offset²

// 转换到世界系（仅考虑yaw）
I_world_effective = R_yaw.transpose() * I_body_effective * R_yaw
```

## 4. 扰动力矩补偿

### 4.1 扰动观测器方程
```
d̂̇ = -K * (I⁻¹ * τ_contact + d̂ + β)
```

其中：
- `d̂`: 扰动估计（角加速度扰动）
- `K`: 观测器增益矩阵
- `τ_contact`: 接触力矩
- `β = K * ω`: 阻尼项

### 4.2 扰动力矩补偿项
```cpp
// 世界系扰动力矩补偿
Vec3<float> τ_disturbance = I_world * d_est;
```

### 4.3 在MPC中的应用
扰动力矩作为前馈项加入到力矩平衡方程中，影响足端力的分配。

## 5. 坐标系转换关系

### 5.1 惯量矩阵转换
```cpp
// 机体系 -> 世界系（完整旋转）
I_world = R_wb.transpose() * I_body * R_wb;

// 机体系 -> Yaw系（仅yaw旋转，MPC中使用）
I_yaw = R_yaw.transpose() * I_body * R_yaw;

// 世界系 -> 机体系
I_body = R_wb * I_world * R_wb.transpose();
```

### 5.2 力和力矩转换
```cpp
// 世界系力 -> 机体系力
F_body = R_wb * F_world;

// 机体系力矩 -> 世界系力矩
τ_world = R_wb.transpose() * τ_body;
```

## 6. MPC动力学矩阵修正

### 6.1 连续时间状态空间矩阵
A矩阵（状态转移）：
```
A = [0   0   0   0   0   0   R_yaw^T  0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   1   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   1   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   1  ]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   x_drag 0  0]
    [0   0   0   0   0   0   0        0   0   0   0   0  ]
    [0   0   0   0   0   0   0        0   0   0   0   1  ]
```

B矩阵（控制输入）：
```cpp
for(int b = 0; b < 4; b++) {
    // 角动力学：使用有效惯量
    B.block(6,b*3,3,3) = cross_mat(I_world_effective.inverse(), r_feet.col(b));
    
    // 线性动力学：使用有效质量
    B.block(9,b*3,3,3) = Matrix<fpt,3,3>::Identity() / m_effective;
}
```

## 7. 重力补偿修正

### 7.1 原始重力项
```
g_vector = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -9.81]^T
```

### 7.2 含负载的重力项
重力项在状态向量的最后一个元素中体现，通过有效质量自动处理：
```cpp
// 在MPC求解中，重力通过以下方式体现：
// F_total = m_effective * (a_desired - g)
```

## 8. 实现要点

### 8.1 数据流
1. DisturbanceEstimator计算mp_fused和d_est
2. 通过接口传递给ConvexMPCLocomotion
3. 更新RobotState中的有效参数
4. MPC求解器使用有效参数进行优化

### 8.2 关键接口
```cpp
// 获取有效质量
float getEffectiveMass() const;

// 获取有效惯量（世界系）
Mat3<float> getEffectiveInertiaWorld() const;

// 获取扰动力矩补偿
Vec3<float> getDisturbanceTorqueCompensation() const;
```

### 8.3 注意事项
1. 坐标系一致性：确保所有计算在正确的坐标系中进行
2. 旋转矩阵方向：注意R_wb是world->body，转置后是body->world
3. 惯量更新频率：每个MPC周期更新一次有效参数
4. 数值稳定性：避免惯量矩阵奇异，添加适当的正则化
