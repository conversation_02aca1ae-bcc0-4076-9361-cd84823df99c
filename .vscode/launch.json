{"version": "0.2.0", "configurations": [{"name": "Debug mit_ctrl with ROS", "type": "cppdbg", "request": "launch", "program": "/bin/bash", "args": ["-c", "source /opt/ros/noetic/setup.bash && exec ${workspaceFolder}/src/build/user/MIT_Controller/mit_ctrl m s"], "stopAtEntry": false, "cwd": "${workspaceFolder}/src/build", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "/usr/bin/gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}]}]}