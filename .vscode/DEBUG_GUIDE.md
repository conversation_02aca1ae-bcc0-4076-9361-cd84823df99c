# MIT Controller 调试指南

## 前置条件

项目已编译完成，`mit_ctrl` 可执行文件存在于 `src/build/user/MIT_Controller/` 目录中。

## 快速调试流程

### 方法1：一键调试（推荐）

1. **直接启动调试**：
   - 按 `F5`
   - 选择 "Debug mit_ctrl (Mini Cheetah Simulation)"
   - 程序将自动在main函数开始调试

### 方法2：分步调试

#### 步骤1：启动仿真器（仅仿真模式需要）

**使用VSCode任务**：
- 按 `Ctrl+Shift+P`
- 输入 "Tasks: Run Task"
- 选择 "start-simulator"

**手动启动**：
```bash
cd src/build
source /opt/ros/noetic/setup.bash
./sim/sim
```

在仿真器界面中：
- 选择 "Mini Cheetah"
- 选择 "Simulator"
- 点击 "Start"

#### 步骤2：设置断点

推荐在以下位置设置断点：
- `src/user/MIT_Controller/main.cpp` 第13行
- `MIT_Controller::runController()` 方法
- `ControlFSM::runFSM()` 方法

#### 步骤3：启动调试

- 按 `F5` 或点击调试面板的播放按钮
- 选择调试配置：
  - "Debug mit_ctrl (Mini Cheetah Simulation)" - 仿真模式
  - "Debug mit_ctrl (Mini Cheetah Robot)" - 真实机器人
  - "Debug mit_ctrl (Cheetah 3 Simulation)" - Cheetah 3仿真

## 调试控制

| 快捷键 | 功能 |
|--------|------|
| `F5` | 继续执行 |
| `F10` | 单步跳过 |
| `F11` | 单步进入 |
| `Shift+F11` | 单步跳出 |
| `Ctrl+Shift+F5` | 重启调试 |
| `Shift+F5` | 停止调试 |

## 监视重要变量

在**监视面板**中添加以下表达式：

```cpp
// 机器人基本参数
_quadruped->_bodyMass
_quadruped->_bodyLength

// 状态估计
_stateEstimate->position
_stateEstimate->vWorld
_stateEstimate->orientation

// 腿部控制
_legController->datas[0].q        // 第0条腿关节角度
_legController->datas[0].qd       // 第0条腿关节速度
_legController->datas[0].p        // 第0条腿足端位置

// 控制状态
_controlFSM->currentState
_controlFSM->nextState
```

## 常见调试场景

### 场景1：程序入口调试
```cpp
// main.cpp - 检查程序启动参数
int main(int argc, char** argv) {
  main_helper(argc, argv, new MIT_Controller());  // 断点
  return 0;
}
```

### 场景2：控制器初始化
```cpp
// MIT_Controller::initializeController() - 检查初始化过程
void MIT_Controller::initializeController() {
  // 断点：观察机器人参数加载、状态估计器初始化
}
```

### 场景3：控制循环
```cpp
// MIT_Controller::runController() - 观察控制周期
void MIT_Controller::runController() {
  // 断点：观察传感器数据、状态估计、控制命令
}
```

### 场景4：状态机转换
```cpp
// ControlFSM::runFSM() - 观察状态转换逻辑
void ControlFSM::runFSM() {
  // 断点：观察当前状态、转换条件、新状态
}
```

## 可用的VSCode任务

- **start-simulator**: 启动仿真器
- **run-mit-ctrl-simulation**: 运行仿真模式控制器
- **run-mit-ctrl-robot**: 运行真实机器人模式
- **open-terminal-with-ros**: 打开已配置ROS环境的终端

## 调试技巧

1. **条件断点**: 右键断点 → 编辑断点 → 添加条件 (如: `iter > 100`)
2. **日志断点**: 不暂停执行，只打印信息
3. **监视表达式**: 实时观察变量变化
4. **调用堆栈**: 查看函数调用链
5. **调试控制台**: 执行GDB命令 (如: `p variable_name`)

## 注意事项

- 控制器运行在1kHz频率，调试时断点会影响实时性
- 仿真模式需要先启动仿真器
- 所有终端都已自动配置ROS环境
- 建议在非关键路径设置断点以避免影响控制性能
