# 电机命令周期监控使用指南

## 简介

这个功能可以帮助您监控控制器实际发送电机命令的周期，用于验证控制频率是否符合预期。

## 快速使用

### 方法1：使用启动脚本（推荐）

```bash
# 进入项目根目录
cd /path/to/03_mitcontrol_latest

# 使用脚本启动（会自动显示周期信息）
./scripts/run_with_period_monitor.sh m r    # Mini Cheetah 真机
./scripts/run_with_period_monitor.sh m s    # Mini Cheetah 仿真
./scripts/run_with_period_monitor.sh 3 r    # Cheetah 3 真机
```

### 方法2：手动设置环境变量

```bash
# 设置环境变量
export SHOW_MOTOR_PERIOD=1

# 正常启动控制器
cd build
./mit_ctrl m r    # 或其他参数
```

### 方法3：临时启用

```bash
# 一次性启用
SHOW_MOTOR_PERIOD=1 ./mit_ctrl m r
```

## 输出示例

启用监控后，终端会显示类似以下信息：

```
[电机命令周期] 开始监控...
[电机命令周期] 2.001 ms, 499.8 Hz
[电机命令周期] 2.000 ms, 500.0 Hz
[电机命令周期] 1.999 ms, 500.3 Hz
[电机命令周期] 2.002 ms, 499.5 Hz
```

## 输出说明

- **周期 (ms)**: 两次电机命令发送之间的时间间隔
- **频率 (Hz)**: 电机命令发送频率
- **更新频率**: 每500次命令打印一次（约每秒一次）

## 预期值

- **Mini Cheetah**: 
  - 目标周期: 2.0 ms
  - 目标频率: 500 Hz
  
- **Cheetah 3**:
  - 目标周期: 1.0 ms  
  - 目标频率: 1000 Hz

## 故障排除

### 1. 周期不稳定
如果看到周期波动很大（如1.5ms到3.0ms），可能原因：
- 系统负载过高
- 实时性配置不当
- 硬件性能不足

### 2. 周期过长
如果周期明显大于目标值，可能原因：
- 控制算法计算时间过长
- I/O操作阻塞
- 系统调度问题

### 3. 没有输出
如果没有看到周期信息：
- 检查环境变量是否设置：`echo $SHOW_MOTOR_PERIOD`
- 确认控制器正常启动
- 检查是否在真机模式下运行

## 技术细节

### 实现位置
- 文件: `src/robot/src/RobotRunner.cpp`
- 函数: `finalizeStep()`
- 测量点: 电机命令发送前

### 时间测量
- 使用 `clock_gettime(CLOCK_MONOTONIC)` 获取高精度时间
- 计算连续两次调用的时间差
- 静态变量保存上次时间戳

### 性能影响
- 监控代码对性能影响极小
- 只在启用时执行测量
- 打印频率限制为每秒一次

## 代码修改说明

如果需要修改监控行为，可以编辑 `RobotRunner.cpp` 中的相关代码：

```cpp
// 修改打印频率（当前每500次打印一次）
if (callCount % 500 == 0) {  // 改为其他值

// 修改输出格式
printf("[电机命令周期] %.3f ms, %.1f Hz\n", periodMs, frequency);
```

## 注意事项

1. **仅在真机测试**: 仿真环境的时间可能不准确
2. **短期监控**: 建议监控10-60秒即可了解周期特性
3. **安全第一**: 真机运行时确保机器人处于安全状态
4. **环境变量**: 记得在需要时设置 `SHOW_MOTOR_PERIOD=1`
