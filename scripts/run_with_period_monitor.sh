#!/bin/bash

# 简单的电机命令周期监控启动脚本
# Simple Motor Command Period Monitor Startup Script

echo "电机命令周期监控启动脚本"
echo "=========================="
echo ""

# 检查参数
if [ $# -lt 2 ]; then
    echo "用法: $0 <机器人类型> <运行模式> [其他参数]"
    echo ""
    echo "机器人类型:"
    echo "  m  - Mini Cheetah"
    echo "  3  - Cheetah 3"
    echo ""
    echo "运行模式:"
    echo "  r  - 真机 (Real Robot)"
    echo "  s  - 仿真 (Simulation)"
    echo ""
    echo "示例:"
    echo "  $0 m r     # Mini Cheetah 真机模式"
    echo "  $0 m s     # Mini Cheetah 仿真模式"
    echo "  $0 3 r     # Cheetah 3 真机模式"
    echo ""
    exit 1
fi

ROBOT_TYPE=$1
RUN_MODE=$2

# 验证参数
if [[ "$ROBOT_TYPE" != "m" && "$ROBOT_TYPE" != "3" ]]; then
    echo "错误: 机器人类型必须是 'm' 或 '3'"
    exit 1
fi

if [[ "$RUN_MODE" != "r" && "$RUN_MODE" != "s" ]]; then
    echo "错误: 运行模式必须是 'r' 或 's'"
    exit 1
fi

# 显示配置
echo "配置信息:"
echo "  机器人类型: $([[ $ROBOT_TYPE == 'm' ]] && echo 'Mini Cheetah' || echo 'Cheetah 3')"
echo "  运行模式: $([[ $RUN_MODE == 'r' ]] && echo '真机' || echo '仿真')"
echo "  周期监控: 启用"
echo ""

# 真机模式安全提醒
if [[ "$RUN_MODE" == "r" ]]; then
    echo "⚠️  警告: 即将在真机模式下运行!"
    echo "   请确保机器人处于安全状态!"
    echo "   按 Ctrl+C 取消，或等待3秒后自动继续..."
    sleep 3
fi

# 设置环境变量启用周期监控
export SHOW_MOTOR_PERIOD=1

echo "正在启动控制器..."
echo "周期信息将每秒显示一次"
echo "按 Ctrl+C 停止程序"
echo ""

# 切换到build目录
cd "$(dirname "$0")/../build" || {
    echo "错误: 无法找到build目录"
    exit 1
}

# 检查可执行文件
if [ ! -f "./mit_ctrl" ]; then
    echo "错误: 未找到 mit_ctrl 可执行文件"
    echo "请先编译项目: make -j4"
    exit 1
fi

# 启动控制器
echo "启动命令: ./mit_ctrl $ROBOT_TYPE $RUN_MODE"
echo ""
./mit_ctrl $ROBOT_TYPE $RUN_MODE

echo ""
echo "控制器已退出"
